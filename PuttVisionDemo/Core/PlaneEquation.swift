//
//  PlaneEquation.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

public struct PlaneEquation {
    public var a: Float  // y = a*x + b*z + c
    public var b: Float
    public var c: Float
    public var normal: simd_float3   // normalized (-a, 1, -b)
    public var slopeAngle: Float     // radians
    public var downhillDirection: simd_float3 // normalized (-a, 0, -b)
    
    public init(a: Float, b: Float, c: Float, normal: simd_float3, slopeAngle: Float, downhillDirection: simd_float3) {
        self.a = a
        self.b = b
        self.c = c
        self.normal = normal
        self.slopeAngle = slopeAngle
        self.downhillDirection = downhillDirection
    }
}
