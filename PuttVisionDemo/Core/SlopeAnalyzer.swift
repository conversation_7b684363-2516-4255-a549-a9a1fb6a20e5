//
//  SlopeAnalyzer.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

public final class SlopeAnalyzer {
    public init() {}

    public func fitPlane(points: [simd_float3]) -> PlaneEquation? {
        guard points.count >= 3 else { return nil }
        
        var sumX: Float = 0, sumZ: Float = 0, sumY: Float = 0
        var sumXX: Float = 0, sumZZ: Float = 0, sumXZ: Float = 0
        var sumXY: Float = 0, sumZY: Float = 0
        
        for p in points {
            sumX += p.x
            sumZ += p.z
            sumY += p.y
            sumXX += p.x*p.x
            sumZZ += p.z*p.z
            sumXZ += p.x*p.z
            sumXY += p.x*p.y
            sumZY += p.z*p.y
        }
        
        let n = Float(points.count)
        
        let A11 = sumXX, A12 = sumXZ, A13 = sumX
        let A21 = sumXZ, A22 = sumZZ, A23 = sumZ
        let A31 = sumX,  A32 = sumZ,  A33 = n
        
        let b1 = sumXY,  b2 = sumZY,  b3 = sumY
        
        let det = A11*(A22*A33 - A23*A32) - A12*(A21*A33 - A23*A31) + A13*(A21*A32 - A22*A31)
        
        guard abs(det) > 1e-8 else {
            let plane = PlaneEquation(a: 0, b: 0, c: sumY/n, normal: simd_float3(0,1,0), slopeAngle: 0, downhillDirection: simd_float3(1,0,0))
            return plane
        }
        
        let a = (b1*(A22*A33 - A23*A32) - A12*(b2*A33 - b3*A32) + A13*(b2*A32 - A22*b3)) / det
        let b = (A11*(b2*A33 - b3*A32) - b1*(A21*A33 - A23*A31) + A13*(A21*b3 - b2*A31)) / det
        let c = (A11*(A22*b3 - b2*A32) - A12*(A21*b3 - b2*A31) + b1*(A21*A32 - A22*A31)) / det
        
        var normal = simd_normalize(simd_float3(-a, 1, -b))
        var A = a, B = b
        if normal.y < 0 { 
            A = -a
            B = -b
            normal = simd_normalize(simd_float3(-A, 1, -B)) 
        }
        
        let gradMag = sqrt(A*A + B*B)
        let slopeAngle = atan(gradMag)
        let downhill = gradMag > 1e-6 ? simd_normalize(simd_float3(-A,0,-B)) : simd_float3(1,0,0)
        
        return PlaneEquation(a: A, b: B, c: c, normal: normal, slopeAngle: slopeAngle, downhillDirection: downhill)
    }
}
