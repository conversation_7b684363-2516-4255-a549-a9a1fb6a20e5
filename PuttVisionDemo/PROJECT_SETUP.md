# PuttVision Demo - 项目设置指南

## Xcode项目配置

### 1. 创建新项目
1. 打开Xcode
2. 选择 "Create a new Xcode project"
3. 选择 "iOS" → "App"
4. 填写项目信息：
   - Product Name: `PuttVisionDemo`
   - Interface: `Storyboard`
   - Language: `Swift`
   - Bundle Identifier: `com.yourcompany.puttvisiondemo`

### 2. 项目设置

#### 2.1 部署目标
- **Deployment Target**: iOS 15.4 或更高
- **Supported Device Families**: iPhone

#### 2.2 必需框架
在 "Frameworks, Libraries, and Embedded Content" 中添加：
- `ARKit.framework`
- `SceneKit.framework`
- `Accelerate.framework` (可选，用于数学计算优化)

#### 2.3 设备能力
在 "Signing & Capabilities" 中确保：
- **Automatically manage signing** 已启用
- 添加 **Camera** capability (如果需要)

#### 2.4 Info.plist 配置
确保包含以下权限描述：
```xml
<key>NSCameraUsageDescription</key>
<string>需要使用相机进行AR推杆轨迹分析</string>
<key>NSLocationWhenInUseUsageDescription</key>
<string>需要位置信息优化AR体验</string>
<key>UIRequiredDeviceCapabilities</key>
<array>
    <string>armv7</string>
    <string>arkit</string>
</array>
```

### 3. 文件导入

#### 3.1 删除默认文件
删除Xcode自动生成的以下文件：
- `ViewController.swift` (如果存在)
- `ContentView.swift` (如果存在)

#### 3.2 导入项目文件
将以下文件夹和文件复制到Xcode项目中：
```
Core/
├── LiDARManager.swift
├── CorridorROISelector.swift
├── SlopeAnalyzer.swift
├── PhysicsSimulator.swift
├── TrajectoryOptimizer.swift
└── PlaneEquation.swift

Rendering/
└── LineRenderer.swift

Interaction/
└── ParameterSmoother.swift

Utils/
├── MathExtensions.swift
└── PhysicsConstants.swift

UI/
└── ARViewController.swift
```

#### 3.3 替换Storyboard文件
- 替换 `Main.storyboard`
- 替换 `LaunchScreen.storyboard`

### 4. 构建设置

#### 4.1 Swift编译器设置
- **Swift Language Version**: Swift 5
- **Optimization Level**: 
  - Debug: `-Onone`
  - Release: `-O`

#### 4.2 其他链接器标志
如果遇到链接问题，可能需要添加：
- `-ObjC` (如果使用Objective-C库)

### 5. 测试设备要求

#### 5.1 推荐设备 (LiDAR支持)
- iPhone 12 Pro
- iPhone 12 Pro Max
- iPhone 13 Pro
- iPhone 13 Pro Max
- iPhone 14 Pro
- iPhone 14 Pro Max
- iPhone 15 Pro
- iPhone 15 Pro Max

#### 5.2 兼容设备 (降级模式)
- iPhone XS 及更新机型
- 支持ARKit的iPad

### 6. 常见问题解决

#### 6.1 编译错误
**错误**: "Use of undeclared type 'ARSCNView'"
**解决**: 确保导入了ARKit和SceneKit框架

**错误**: "Cannot find 'simd_float3' in scope"
**解决**: 确保导入了simd模块

#### 6.2 运行时错误
**错误**: "This app has crashed because it attempted to access privacy-sensitive data"
**解决**: 检查Info.plist中的相机权限描述

**错误**: ARKit初始化失败
**解决**: 确保在真机上测试，模拟器不支持ARKit

#### 6.3 性能问题
**问题**: 帧率下降
**解决**: 
- 减少点云采样频率
- 降低轨迹渲染精度
- 优化物理计算频率

### 7. 调试技巧

#### 7.1 控制台输出
应用会输出以下调试信息：
```
Point cloud size: 1234
Corridor points: 567
LiDAR available: true
```

#### 7.2 性能监控
在Xcode中使用以下工具：
- **Instruments** → **Time Profiler** (CPU性能)
- **Instruments** → **Allocations** (内存使用)
- **Xcode Debug Navigator** (实时性能)

#### 7.3 AR调试
启用ARKit调试选项：
```swift
// 在setupAR()中添加
arView.debugOptions = [.showFeaturePoints, .showWorldOrigin]
```

### 8. 发布准备

#### 8.1 代码签名
- 确保有效的开发者证书
- 配置正确的Provisioning Profile
- 设置正确的Bundle Identifier

#### 8.2 优化设置
- Release配置使用 `-O` 优化
- 启用 "Strip Debug Symbols"
- 启用 "Dead Code Stripping"

#### 8.3 测试清单
- [ ] 在多种LiDAR设备上测试
- [ ] 验证非LiDAR设备的降级功能
- [ ] 测试不同光照条件
- [ ] 验证内存使用和性能
- [ ] 测试长时间使用稳定性

## 快速开始

1. 在Xcode中创建新项目
2. 按照上述配置设置项目
3. 导入所有源文件
4. 在支持LiDAR的iPhone上运行
5. 开始测试推杆轨迹功能

## 技术支持

如果遇到问题，请检查：
1. 设备是否支持ARKit
2. iOS版本是否满足要求
3. 相机权限是否已授予
4. 网络连接是否正常（用于AR云锚点，如果使用）
