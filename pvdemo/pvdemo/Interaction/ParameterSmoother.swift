//
//  ParameterSmoother.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import Foundation
import simd

public final class ParameterSmoother {
    private var smoothedA: Float = 0
    private var smoothedB: Float = 0
    private var smoothedC: Float = 0
    private var initialized = false
    private let alpha: Float

    public init(alpha: Float = 0.2) { 
        self.alpha = alpha 
    }

    public func smooth(_ plane: PlaneEquation) -> PlaneEquation {
        if !initialized {
            smoothedA = plane.a
            smoothedB = plane.b
            smoothedC = plane.c
            initialized = true
            return plane
        }
        
        smoothedA = alpha * plane.a + (1 - alpha) * smoothedA
        smoothedB = alpha * plane.b + (1 - alpha) * smoothedB
        smoothedC = alpha * plane.c + (1 - alpha) * smoothedC

        // Upward constraint: keep normal.y > 0 (flip a,b if needed)
        var a = smoothedA, b = smoothedB, c = smoothedC
        var normal = simd_normalize(simd_float3(-a, 1, -b))
        if normal.y < 0 {
            a = -a
            b = -b
            normal = simd_normalize(simd_float3(-a, 1, -b))
            smoothedA = a
            smoothedB = b // keep c as is
        }
        
        let gradMag = sqrt(a*a + b*b)
        let slopeAngle = atan(gradMag)
        let downhill = gradMag > 1e-6 ? simd_normalize(simd_float3(-a,0,-b)) : simd_float3(1,0,0)
        
        return PlaneEquation(a: a, b: b, c: c, normal: normal, slopeAngle: slopeAngle, downhillDirection: downhill)
    }

    public func reset() { 
        initialized = false 
    }
}
