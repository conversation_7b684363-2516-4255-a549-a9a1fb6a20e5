//
//  CoreAlgorithmTests.swift
//  PuttVisionDemo
//
//  Created on 2025-01-09.
//  Copyright © 2025 PuttVision. All rights reserved.
//

import XCTest
import simd
@testable import PuttVisionDemo

class CoreAlgorithmTests: XCTestCase {

    func testPlaneEquationCreation() {
        let plane = PlaneEquation(
            a: 0.1, b: 0.05, c: 1.0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0.1,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        XCTAssertEqual(plane.a, 0.1, accuracy: 1e-6)
        XCTAssertEqual(plane.b, 0.05, accuracy: 1e-6)
        XCTAssertEqual(plane.c, 1.0, accuracy: 1e-6)
    }

    func testSlopeAnalyzerWithFlatPlane() {
        let analyzer = SlopeAnalyzer()
        
        // 创建平坦地面的点云 (y = 0)
        let points = [
            simd_float3(0, 0, 0),
            simd_float3(1, 0, 0),
            simd_float3(0, 0, 1),
            simd_float3(1, 0, 1),
            simd_float3(0.5, 0, 0.5)
        ]
        
        let plane = analyzer.fitPlane(points: points)
        
        XCTAssertNotNil(plane)
        XCTAssertEqual(plane!.a, 0, accuracy: 1e-3)
        XCTAssertEqual(plane!.b, 0, accuracy: 1e-3)
        XCTAssertEqual(plane!.c, 0, accuracy: 1e-3)
        XCTAssertEqual(plane!.slopeAngle, 0, accuracy: 1e-3)
    }

    func testSlopeAnalyzerWithSlopedPlane() {
        let analyzer = SlopeAnalyzer()
        
        // 创建倾斜地面的点云 (y = 0.1*x)
        let points = [
            simd_float3(0, 0, 0),
            simd_float3(1, 0.1, 0),
            simd_float3(2, 0.2, 0),
            simd_float3(0, 0, 1),
            simd_float3(1, 0.1, 1)
        ]
        
        let plane = analyzer.fitPlane(points: points)
        
        XCTAssertNotNil(plane)
        XCTAssertEqual(plane!.a, 0.1, accuracy: 1e-2)
        XCTAssertEqual(plane!.b, 0, accuracy: 1e-2)
        XCTAssertGreaterThan(plane!.slopeAngle, 0)
    }

    func testCorridorROISelector() {
        let selector = CorridorROISelector()
        
        let ball = simd_float3(0, 0, 0)
        let hole = simd_float3(3, 0, 0)
        
        // 创建点云：一些在走廊内，一些在走廊外
        let pointCloud = [
            simd_float3(1, 0, 0),     // 走廊内
            simd_float3(2, 0, 0),     // 走廊内
            simd_float3(1.5, 0, 0.2), // 走廊内
            simd_float3(1, 0, 1),     // 走廊外（太远）
            simd_float3(-1, 0, 0),    // 走廊外（球后面）
            simd_float3(5, 0, 0)      // 走廊外（洞前面太远）
        ]
        
        let corridorPoints = selector.selectCorridor(
            pointCloud: pointCloud,
            ball: ball,
            hole: hole,
            width: 0.45
        )
        
        // 应该只有前3个点在走廊内
        XCTAssertEqual(corridorPoints.count, 3)
    }

    func testPhysicsSimulator() {
        let simulator = PhysicsSimulator()
        
        // 创建平坦地面
        let plane = PlaneEquation(
            a: 0, b: 0, c: 0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let trajectory = simulator.simulate(
            initialPosition: simd_float3(0, 0, 0),
            launchAngle: 0,
            launchSpeed: 2.0,
            plane: plane
        )
        
        XCTAssertGreaterThan(trajectory.count, 1)
        XCTAssertEqual(trajectory.first!.x, 0, accuracy: 1e-6)
        XCTAssertEqual(trajectory.first!.z, 0, accuracy: 1e-6)
        
        // 最后一个点应该在前方
        XCTAssertGreaterThan(trajectory.last!.z, 0)
    }

    func testTrajectoryOptimizer() {
        let optimizer = TrajectoryOptimizer()
        
        let ball = simd_float3(0, 0, 0)
        let hole = simd_float3(0, 0, 3)
        
        // 平坦地面
        let plane = PlaneEquation(
            a: 0, b: 0, c: 0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let optimalAngle = optimizer.findOptimalAngle(
            ball: ball,
            hole: hole,
            plane: plane
        )
        
        // 在平坦地面上，最优角度应该接近0
        XCTAssertEqual(optimalAngle, 0, accuracy: 0.1)
    }

    func testParameterSmoother() {
        let smoother = ParameterSmoother(alpha: 0.5)
        
        let plane1 = PlaneEquation(
            a: 0.1, b: 0.05, c: 1.0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0.1,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let plane2 = PlaneEquation(
            a: 0.2, b: 0.1, c: 1.1,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0.2,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let smoothed1 = smoother.smooth(plane1)
        let smoothed2 = smoother.smooth(plane2)
        
        // 第一次应该返回原值
        XCTAssertEqual(smoothed1.a, plane1.a, accuracy: 1e-6)
        
        // 第二次应该是平滑后的值
        XCTAssertGreaterThan(smoothed2.a, plane1.a)
        XCTAssertLessThan(smoothed2.a, plane2.a)
    }

    func testSpeedEstimation() {
        let optimizer = TrajectoryOptimizer()
        
        let speed1 = optimizer.estimateBaseSpeed(distance: 1.0)
        let speed2 = optimizer.estimateBaseSpeed(distance: 2.0)
        
        XCTAssertGreaterThan(speed1, 0)
        XCTAssertGreaterThan(speed2, speed1) // 距离越远，速度越大
    }

    func testHoleHitDetection() {
        let simulator = PhysicsSimulator()
        let hole = simd_float3(0, 0, 1)
        
        // 创建一个经过洞口的轨迹
        let trajectory = [
            simd_float3(0, 0, 0.8),
            simd_float3(0, 0, 0.95), // 接近洞口
            simd_float3(0, 0, 1.05), // 经过洞口
            simd_float3(0, 0, 1.2)
        ]
        
        let hit = simulator.checkHoleHit(trajectory: trajectory, hole: hole)
        XCTAssertTrue(hit)
        
        // 创建一个错过洞口的轨迹
        let missTrajectory = [
            simd_float3(0, 0, 0.8),
            simd_float3(0.2, 0, 0.95), // 偏离洞口
            simd_float3(0.2, 0, 1.05),
            simd_float3(0.2, 0, 1.2)
        ]
        
        let miss = simulator.checkHoleHit(trajectory: missTrajectory, hole: hole)
        XCTAssertFalse(miss)
    }
}
