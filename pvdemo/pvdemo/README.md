# PuttVision Demo

基于LiDAR的高精度推杆轨迹AR应用

## 项目概述

PuttVision是一个使用增强现实技术的高尔夫推杆轨迹分析应用。通过LiDAR扫描地形，精确计算推杆轨迹，帮助高尔夫爱好者提高推杆技术。

## 核心功能

- **实时地形扫描**: 使用LiDAR获取高精度点云数据
- **智能轨迹计算**: 基于物理模拟的推杆路径预测
- **AR可视化**: 在真实环境中显示推杆轨迹线
- **手动标记**: 点击标记球和洞的位置

## 技术特性

### 核心算法
- **走廊ROI采样**: 精确提取球到洞路径的相关点云
- **平面拟合**: 使用正规方程求解地面坡度
- **物理模拟**: 显式积分计算球的运动轨迹
- **角度优化**: 黄金分割搜索最优发射角度

### 技术栈
- **iOS**: 15.4+
- **语言**: Swift
- **框架**: ARKit, SceneKit, simd
- **设备**: 支持LiDAR的iPhone (12 Pro/Max, 13 Pro/Max, 14 Pro/Max)

## 项目结构

```
PuttVisionDemo/
├── Core/                    # 核心算法
│   ├── LiDARManager.swift          # LiDAR数据获取
│   ├── CorridorROISelector.swift   # 走廊ROI采样
│   ├── SlopeAnalyzer.swift         # 平面拟合
│   ├── PhysicsSimulator.swift      # 物理模拟
│   ├── TrajectoryOptimizer.swift   # 轨迹优化
│   └── PlaneEquation.swift         # 平面方程结构
├── Rendering/               # 渲染模块
│   └── LineRenderer.swift          # 轨迹线渲染
├── Interaction/             # 交互处理
│   └── ParameterSmoother.swift     # 参数平滑
├── Utils/                   # 工具类
│   ├── MathExtensions.swift        # 数学扩展
│   └── PhysicsConstants.swift      # 物理常数
└── UI/                      # 用户界面
    └── ARViewController.swift      # 主控制器
```

## 使用方法

1. **启动应用**: 在支持LiDAR的iPhone上运行
2. **扫描环境**: 缓慢移动设备扫描推杆区域
3. **标记球位置**: 点击屏幕标记高尔夫球位置
4. **标记洞位置**: 点击屏幕标记球洞位置
5. **查看轨迹**: 应用自动计算并显示推杆轨迹线
6. **重新开始**: 点击"重置"按钮重新标记

## 系统要求

### 最佳体验
- iPhone 12 Pro/Max 或更新机型
- iOS 15.4 或更高版本
- LiDAR传感器

### 降级支持
- 非LiDAR设备可运行，但精度降低
- 使用平面检测替代点云分析

## 开发说明

### 关键参数
- **走廊宽度**: 0.45m (可调整)
- **时间步长**: 0.003s
- **滚动摩擦**: 0.9 m/s² (可调整)
- **最小速度**: 0.05 m/s (停止阈值)

### 坐标系
- **世界坐标**: ARKit标准 (Y向上)
- **平面方程**: y = ax + bz + c
- **法向量**: (-a, 1, -b) 归一化

### 调试信息
应用会在控制台输出：
- 点云数据大小
- 走廊ROI点数
- 平面拟合结果
- 轨迹计算状态

## 已知限制

1. **距离范围**: 0.5-15米
2. **坡度限制**: 建议小于30度
3. **点云要求**: 至少20个有效点
4. **环境光线**: 需要充足光线进行AR追踪

## 故障排除

### "点云不足"错误
- 缓慢移动设备扫描更多区域
- 确保在LiDAR设备上运行
- 检查环境光线是否充足

### "平面拟合失败"错误
- 确保在相对平坦的表面使用
- 避免过于复杂的地形
- 重新扫描获取更多点云数据

### AR追踪丢失
- 确保环境有足够的特征点
- 避免在纯色或反光表面使用
- 重启应用重新初始化AR会话

## 版本历史

### v1.0.0 (2025-01-09)
- 初始版本发布
- 基础LiDAR点云处理
- 物理模拟和轨迹优化
- AR可视化功能

## 许可证

Copyright © 2025 PuttVision. All rights reserved.
