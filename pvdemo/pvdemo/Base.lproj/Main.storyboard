<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--ARView Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ARViewController" customModule="PuttVisionDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <arscnView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BrB-h1-WRS">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </arscnView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="点击标记球位置" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5XD-lD-9ct">
                                <rect key="frame" x="20" y="79" width="353" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="Hep-Wd-cEh"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" systemColor="whiteColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ncc-fO-9jI">
                                <rect key="frame" x="20" y="768" width="80" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="8bX-Wd-cEh"/>
                                    <constraint firstAttribute="width" constant="80" id="Ncc-fO-9jI-width"/>
                                </constraints>
                                <state key="normal" title="重置">
                                    <color key="titleColor" systemColor="whiteColor"/>
                                </state>
                                <connections>
                                    <action selector="resetButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Ncc-fO-9jI-action"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutMarginsGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="BrB-h1-WRS" firstAttribute="top" secondItem="8bC-Xf-vdC" secondAttribute="top" id="BrB-h1-WRS-top"/>
                            <constraint firstItem="BrB-h1-WRS" firstAttribute="leading" secondItem="8bC-Xf-vdC" secondAttribute="leading" id="BrB-h1-WRS-leading"/>
                            <constraint firstAttribute="trailing" secondItem="BrB-h1-WRS" secondAttribute="trailing" id="BrB-h1-WRS-trailing"/>
                            <constraint firstAttribute="bottom" secondItem="BrB-h1-WRS" secondAttribute="bottom" id="BrB-h1-WRS-bottom"/>
                            <constraint firstItem="5XD-lD-9ct" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="5XD-lD-9ct-top"/>
                            <constraint firstItem="5XD-lD-9ct" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="5XD-lD-9ct-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="5XD-lD-9ct" secondAttribute="trailing" constant="20" id="5XD-lD-9ct-trailing"/>
                            <constraint firstItem="Ncc-fO-9jI" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="Ncc-fO-9jI-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="Ncc-fO-9jI" secondAttribute="bottom" constant="10" id="Ncc-fO-9jI-bottom"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="arView" destination="BrB-h1-WRS" id="BrB-h1-WRS-outlet"/>
                        <outlet property="instructionLabel" destination="5XD-lD-9ct" id="5XD-lD-9ct-outlet"/>
                        <outlet property="resetButton" destination="Ncc-fO-9jI" id="Ncc-fO-9jI-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20" y="4"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="whiteColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
